import { createRouter, createWebHistory } from "vue-router";
import firebase from "firebase/app";
import "firebase/auth";
import store from "../store";

const routes = [
  {
    path: "",
    component: () => import("@/layouts/AuthLayout.vue"),

    children: [
      // ===============
      // dashboard
      // ===============
      {
        path: "",
        name: "Dashboard",
        component: () =>
          import(
            /* webpackChunkName: "dashboard" */ "../views/dashboard/MainDashboard.vue"
          ),
        meta: {
          title: "Dashboard", //Shown as tab title for this route
          requiresAuth: true, //meta data to check user logged in
        },
      },
      //this route is added for update alert for mobile app
      {
        path: "/dashboard",
        name: "Layout",
        component: () =>
          import(
            /* webpackChunkName: "dashboard" */ "../views/dashboard/MainDashboard.vue"
          ),
        meta: {
          title: "Layout", //Shown as tab title for this route
          requiresAuth: true, //meta data to check user logged in
        },
      },
      {
        path: "/quick-menu",
        name: "Quick Menu",
        component: () =>
          import(
            /* webpackChunkName: "quick-menu" */ "../layouts/QuickMenu.vue"
          ),
        meta: {
          title: "Quick Menu", //Shown as tab title for this route
          requiresAuth: true, //meta data to check user logged in
        },
      },
      // ===============
      // workflow
      // ===============
      {
        path: "/approvals/approval-management",
        name: "Approval Management",
        component: () =>
          import(
            /* webpackChunkName: "Approval Management" */ "../views/workflow/approval-management/ApprovalManagement.vue"
          ),
        meta: {
          title: "Approval Management",
          requiresAuth: true,
        },
      },

      {
        path: "/workflow/dynamic-form-builder",
        name: "Dynamic Form Builder",
        component: () =>
          import(
            /* webpackChunkName: "Dynamic Form Builder" */ "../views/workflow/dynamic-form-builder/DynamicFormBuilder.vue"
          ),
        meta: {
          title: "Dynamic Form Builder",
          requiresAuth: true,
        },
      },
      {
        path: "/workflow/workflow-builder",
        name: "Workflow Builder",
        component: () =>
          import(
            "../views/workflow/workflow-builder/workflow-list/WorkflowList.vue"
          ),
        meta: {
          title: "Workflow Builder",
          requiresAuth: true,
        },
      },
      // ===============
      // benefits
      // ===============
      {
        path: "/benefits/air-ticket-claim",
        name: "Air Ticket Claim",
        component: () =>
          import(
            /* webpackChunkName: "Air Ticket Claim" */ "../views/benefits/air-fair-claim/AirFairClaim.vue"
          ),
        meta: {
          title: "Air Ticket Claim",
          requiresAuth: true,
        },
      },

      // ===============
      // onboarding
      // ===============
      {
        path: "onboarding/individuals",
        name: "Invited Individuals",
        component: () =>
          import(
            /* webpackChunkName: "Invited Individuals" */ "../views/onboarding/individuals/invited-individuals/InvitedIndividual.vue"
          ),
        meta: {
          title: "Invited Individuals",
          requiresAuth: true,
        },
      },
      {
        path: "onboarding/onboarded-individuals",
        name: "Onboarded Individuals",
        component: () =>
          import(
            /* webpackChunkName: "Onboarded Individuals" */ "../views/onboarding/individuals/onboarded-individuals/OnboardedIndividual.vue"
          ),
        meta: {
          title: "Onboarded Individuals",
          requiresAuth: true,
        },
      },

      // ===============
      // settings
      // ===============
      {
        path: "/settings/payroll/salary-components",
        name: "Salary Components",
        component: () =>
          import(
            /* webpackChunkName: "Salary Components" */ "../views/settings/payroll/salary-components/SalaryComponents.vue"
          ),
        meta: {
          title: "Salary Components",
          requiresAuth: true,
        },
      },
      {
        path: "/settings/integration",
        name: "Job Board Integration",
        component: () =>
          import(
            /* webpackChunkName: "Job Board Integration" */ "../views/settings/integration/recruitment/JobPostings.vue"
          ),
        meta: {
          title: "Job Board Integration",
          requiresAuth: true,
        },
      },
      {
        path: "/settings/integration/recruitment",
        name: "Company sign up page",
        component: () =>
          import(
            /* webpackChunkName: "Company sign up page" */ "../views/settings/integration/CompanySignUpPage.vue"
          ),
        meta: {
          title: "Company sign up page",
          requiresAuth: true,
        },
      },
      {
        path: "/settings/core-hr/comp-off",
        name: "Comp Off",
        component: () =>
          import(
            /* webpackChunkName: "Comp Off" */ "../views/settings/coreHr/comp-off/CompOff.vue"
          ),
        meta: {
          title: "Comp Off",
          requiresAuth: true,
        },
      },
      {
        path: "/settings/core-hr/overtime",
        name: "Overtime",
        component: () =>
          import(
            /* webpackChunkName: "Overtime" */ "../views/settings/coreHr/overtime/OverTime.vue"
          ),
        meta: {
          title: "Overtime",
          requiresAuth: true,
        },
      },
      {
        path: "/settings/core-hr/employee-number-series",
        name: "Employee Number Series",
        component: () =>
          import(
            /* webpackChunkName: "Employee Number Series" */ "../views/settings/coreHr/employee-number-series/EmployeeNumberSeries.vue"
          ),
        meta: {
          title: "Employee Number Series",
          requiresAuth: true,
        },
      },
      {
        path: "/settings/core-hr/attendance-configuration",
        name: "Attendance Configuration",
        component: () =>
          import(
            /* webpackChunkName: "attendance-configuration" */ "../views/settings/coreHr/attendance-configuration/AttendanceConfiguration.vue"
          ),
        meta: {
          title: "Attendance Configuration",
          requiresAuth: true,
        },
      },
      {
        path: "/settings/core-hr/roster",
        name: "Roster",
        component: () =>
          import(
            /* webpackChunkName: "Roster" */ "../views/settings/coreHr/dynamic-weekoff/Roster.vue"
          ),
        meta: {
          title: "Roster",
          requiresAuth: true,
        },
      },
      {
        path: "/settings/core-hr/pre-approvals",
        name: "Pre-Approvals",
        component: () =>
          import(
            /* webpackChunkName: "Pre Approvals" */ "../views/settings/coreHr/pre-approvals/PreApprovals.vue"
          ),
        meta: {
          title: "Pre-Approvals",
          requiresAuth: true,
        },
      },
      {
        path: "/settings/core-hr/roles",
        name: "Roles",
        component: () =>
          import(
            /* webpackChunkName: "Roles" */ "../views/settings/coreHr/roles/Roles.vue"
          ),
        meta: {
          title: "Roles",
          requiresAuth: true,
        },
      },
      {
        path: "/settings/core-hr/special-wages",
        name: "Special Wages",
        component: () =>
          import(
            /* webpackChunkName: "Special Wages" */ "../views/settings/coreHr/special-wages/SpecialWages.vue"
          ),
        meta: {
          title: "Special Wages",
          requiresAuth: true,
        },
      },
      {
        path: "/settings/core-hr/lop-recovery",
        name: "LOP Recovery - Settings",
        component: () =>
          import(
            /* webpackChunkName: "LOP Recovery - Settings" */ "../views/settings/coreHr/lop-recovery/LopRecovery.vue"
          ),
        meta: {
          title: "LOP Recovery - Settings",
          requiresAuth: true,
        },
      },
      // ===============
      // settings / data-loss-prevention
      // ===============
      {
        path: "/settings/data-loss-prevention/location-tracking",
        name: "Location Tracking - Settings",
        component: () =>
          import(
            /* webpackChunkName: "Location Tracking - Settings" */ "../views/settings/data-loss-prevention/location-tracking/LocationTracking.vue"
          ),
        meta: {
          title: "Location Tracking - Settings",
          requiresAuth: true,
        },
      },
      {
        path: "settings/data-loss-prevention/key-logging",
        name: "Key Logging - Settings",
        component: () =>
          import(
            /* webpackChunkName: "Key Logging - Settings" */ "../views/settings/data-loss-prevention/key-logging/KeyLogging.vue"
          ),
        meta: {
          title: "Key Logging - Settings",
          requiresAuth: true,
        },
      },
      {
        path: "settings/data-loss-prevention/additional-screenshots",
        name: "Additional Screenshots - Settings",
        component: () =>
          import(
            /* webpackChunkName: "Additional Screenshots - Settings" */ "../views/settings/data-loss-prevention/additional-screenshots/AdditionalScreenshots.vue"
          ),
        meta: {
          title: "Additional Screenshots - Settings",
          requiresAuth: true,
        },
      },
      {
        path: "settings/data-loss-prevention/audit-log",
        name: "Audit Log - Data Loss Prevention",
        component: () =>
          import(
            /* webpackChunkName: "Audit Log - Data Loss Prevention" */ "../views/settings/data-loss-prevention/audit-log/DataLossPreventionAuditLog.vue"
          ),
        meta: {
          title: "Audit Log - Data Loss Prevention",
          requiresAuth: true,
        },
      },
      {
        path: "settings/productivity-monitoring/audit-log",
        name: "Audit Log - Productivity Monitoring",
        component: () =>
          import(
            /* webpackChunkName: "Audit Log - Productivity Monitoring" */ "../views/settings/productivity-monitoring/ProductivityAuditLog.vue"
          ),
        meta: {
          title: "Audit Log - Productivity Monitoring",
          requiresAuth: true,
        },
      },
      {
        path: "productivity-monitoring/members/audit-log",
        name: "Audit Log - Members",
        component: () =>
          import(
            /* webpackChunkName: "Audit Log - Members" */ "../views/productivity-monitoring/members/MembersAuditLog.vue"
          ),
        meta: {
          title: "Audit Log - Members",
          requiresAuth: true,
        },
      },
      // ===============
      // org-structure
      // ===============

      {
        path: "/core-hr/org-structure",
        name: "Org Structure",
        component: () =>
          import(
            /* webpackChunkName: "org-structure" */ "../views/coreHr/org-structure/OrgStructureSetup.vue"
          ),
        meta: {
          title: "Org Structure",
          requiresAuth: true,
        },
      },
      {
        path: "/core-hr/locations",
        name: "Locations",
        component: () =>
          import(
            /* webpackChunkName: "Locations - Organization" */ "../views/organization/locations/LocationOrg.vue"
          ),
        meta: {
          title: "Locations",
          requiresAuth: true,
        },
      },
      {
        path: "/core-hr/grades",
        name: "Grades",
        component: () =>
          import(
            /* webpackChunkName: "Grades - Organization" */ "../views/organization/employee-grade/EmployeesGrades.vue"
          ),
        meta: {
          title: "Grades",
          requiresAuth: true,
        },
      },
      {
        path: "/core-hr/employee-type",
        name: "Employee Type",
        component: () =>
          import(
            /* webpackChunkName: "Employee Type - Organization" */ "../views/organization/employee-type/EmployeesType.vue"
          ),
        meta: {
          title: "Employee Type",
          requiresAuth: true,
        },
      },
      // ===============
      // Payroll Data Management
      // ===============
      {
        path: "/core-hr/payroll-data-management",
        name: "Payroll Data Management",
        component: () =>
          import(
            /* webpackChunkName: "payroll-data-management" */ "../views/coreHr/payroll-data-management/PayrollDataManagementSetup.vue"
          ),
        meta: {
          title: "Payroll Data Management",
          requiresAuth: true,
        },
      },
      {
        path: "/core-hr/air-ticketing-policy",
        name: "Air Ticketing Policy",
        component: () =>
          import(
            /* webpackChunkName: "Air Ticketing Policy" */ "../views/coreHr/payroll-data-management/air-ticket-policy/AirTicketingPolicy.vue"
          ),
        meta: {
          title: "Air Ticketing Policy",
          requiresAuth: true,
        },
      },
      {
        path: "/core-hr/air-ticket-settlement-summary",
        name: "Air Ticket Settlement Summary",
        component: () =>
          import(
            /* webpackChunkName: "Air Ticket Settlement Summary" */ "../views/coreHr/payroll-data-management/air-ticket-settlement-summary/AirTicketSettlementSummary.vue"
          ),
        meta: {
          title: "Air Ticket Settlement Summary",
          requiresAuth: true,
        },
      },
      {
        path: "/core-hr/travel-and-expense",
        name: "Travel and Expenses",
        component: () =>
          import(
            /* webpackChunkName: "Travel and Expenses" */ "../views/coreHr/payroll-data-management/currency-conversion/CurrencyConversion.vue"
          ),
        meta: {
          title: "Travel and Expenses",
          requiresAuth: true,
        },
      },
      {
        path: "/core-hr/payroll-data-management/salary-info",
        name: "Salary Info",
        component: () =>
          import(
            /* webpackChunkName: "Salary Info" */ "@/views/coreHr/payroll-data-management/salary-info/SalaryInfo.vue"
          ),
        meta: {
          title: "Salary Info",
          requiresAuth: true,
        },
      },
      {
        path: "/core-hr/reports",
        name: " Reports ",
        component: () =>
          import(
            /* webpackChunkName: "Reports" */ "../views/coreHr/payroll-data-management/core-hr-reports/CorehrReports.vue"
          ),
        meta: {
          title: "Reports",
          requiresAuth: true,
        },
      },
      {
        path: "/settings/data-loss-prevention/internet-access-control",
        name: "Internet Access Control - Settings",
        component: () =>
          import(
            /* webpackChunkName: "Internet Access Control - Settings" */ "../views/settings/data-loss-prevention/internet-access-control/InternetAccessControl.vue"
          ),
        meta: {
          title: "Internet Access Control - Settings",
          requiresAuth: true,
        },
      },
      {
        path: "/settings/payroll",
        name: "Payroll settings",
        component: () =>
          import(
            /* webpackChunkName: "Payroll settings" */ "../views/settings/payroll/payroll-general-settings/PayrollSettingsMainForm.vue"
          ),
        meta: {
          title: "Payroll settings",
          requiresAuth: true,
        },
      },
      {
        path: "/settings/payroll/general",
        name: "Payroll General settings",
        component: () =>
          import(
            /* webpackChunkName: "Payroll General settings" */ "../views/settings/payroll/payroll-general-settings/PayrollGeneralSettingsMain.vue"
          ),
        meta: {
          title: "Payroll General settings",
          requiresAuth: true,
        },
      },
      {
        path: "/settings/payroll/salary-template",
        name: "Salary Template",
        component: () =>
          import(
            /* webpackChunkName: "Salary Template" */ "@/views/settings/payroll/salary-template/SalaryTemplate.vue"
          ),
        meta: {
          title: "Salary Template",
          requiresAuth: true,
        },
      },
      // ======================
      // settings - recruitment
      // ======================
      {
        path: "/settings/general",
        name: "Settings - General",
        component: () =>
          import(
            /* webpackChunkName: "Settings - General" */ "../views/settings/general/SettingsGeneralSetup.vue"
          ),
        meta: {
          title: "Settings - General",
          requiresAuth: true,
        },
      },
      {
        path: "/settings/general/general-settings",
        name: "General Settings",
        component: () =>
          import(
            /* webpackChunkName: "General Settings" */ "../views/settings/general/general-settings/GeneralSettings.vue"
          ),
        meta: {
          title: "General Settings",
          requiresAuth: true,
        },
      },
      {
        path: "/settings/general/custom-fields",
        name: "Custom Fields",
        component: () =>
          import(
            /* webpackChunkName: "Custom Fields" */ "../views/settings/general/custom-fields/CustomFields.vue"
          ),
        meta: {
          title: "Custom Fields",
          requiresAuth: true,
        },
      },
      {
        path: "/settings/general/email-templates",
        name: "Email Templates",
        component: () =>
          import(
            /* webpackChunkName: "Email Templates" */ "../views/settings/general/email-template/EmailTemplate.vue"
          ),
        meta: {
          title: "Email Templates",
          requiresAuth: true,
        },
      },
      {
        path: "/settings/general/custom-reports",
        name: "Custom Reports",
        component: () =>
          import(
            /* webpackChunkName: "Custom Reports" */ "../views/settings/general/custom-reports/CustomReports.vue"
          ),
        meta: {
          title: "Custom Reports",
          requiresAuth: true,
        },
      },
      // ======================
      // settings - recruitment
      // ======================
      {
        path: "/settings/recruitment",
        name: "Settings - Recruitment",
        component: () =>
          import(
            /* webpackChunkName: "Settings - Recruitment" */ "../views/settings/recruitment/RecruitmentSetup.vue"
          ),
        meta: {
          title: "Settings - Recruitment",
          requiresAuth: true,
        },
      },
      {
        path: "/settings/recruitment/hiring-flow",
        name: "Hiring Flow",
        component: () =>
          import(
            /* webpackChunkName: "Hiring Flow" */ "../views/settings/recruitment/hiringflow/DefaultHiringFlow.vue"
          ),
        meta: {
          title: "Hiring Flow",
          requiresAuth: true,
        },
      },
      {
        path: "/settings/recruitment/career-page-designer",
        name: "Career Page Designer",
        component: () =>
          import(
            /* webpackChunkName: "Career Page Designer" */ "../views/settings/recruitment/career-page-designer/CareersPageConfiguration.vue"
          ),
        meta: {
          title: "Career Page Designer",
          requiresAuth: true,
        },
      },
      {
        path: "/settings/recruitment/general",
        name: "Recruitment Settings",
        component: () =>
          import(
            /* webpackChunkName: "Recruitment Settings" */ "../views/settings/recruitment/recruitment-settings/RecruitmentSettings.vue"
          ),
        meta: {
          title: "Recruitment Settings",
          requiresAuth: true,
        },
      },
      // ======================
      // employee-self-service
      // ======================
      {
        path: "employee-self-service/pre-approval",
        name: "Pre Approval - Employee",
        component: () =>
          import(
            /* webpackChunkName: "Pre Approval - Employee" */ "../views/employee-self-service/pre-approval-details/PreApproval"
          ),
        meta: {
          title: "Pre Approval - Employee",
          requiresAuth: true,
        },
      },
      {
        path: "employee-self-service/lop-recovery",
        name: "LOP Recovery - Employee",
        component: () =>
          import(
            /* webpackChunkName: "LOP Recovery - Employee" */ "../views/employee-self-service/lop-recovery/LopRecoveryMainForm.vue"
          ),
        meta: {
          title: "LOP Recovery - Employee",
          requiresAuth: true,
        },
      },
      {
        path: "employee-self-service/compensatory-off-balance",
        name: "Comp Off Balance - Employee",
        component: () =>
          import(
            /* webpackChunkName: "Comp Off Balance - Employee" */ "../views/employee-self-service/comp-off-balance/CompOffBalanceMain.vue"
          ),
        meta: {
          title: "Comp Off Balance - Employee",
          requiresAuth: true,
        },
      },
      {
        path: "/employee-self-service/timesheets",
        name: "Timesheets",
        component: () =>
          import(
            /* webpackChunkName: "Timesheets" */ "../views/employee-self-service/timesheets/Timesheets.vue"
          ),
        meta: {
          title: "Timesheets",
          requiresAuth: true,
        },
      },
      {
        path: "/employee-self-service/organization-chart",
        name: "Organization Chart",
        component: () =>
          import(
            /* webpackChunkName: "organization-chart" */ "../views/employee-self-service/organization-chart/OrganizationChart.vue"
          ),
        meta: {
          title: "Organization Chart",
          requiresAuth: true,
        },
      },
      // My finance
      {
        path: "/my-finance/my-declarations",
        name: "My Declarations",
        component: () =>
          import(
            /* webpackChunkName: "My Declarations" */ "../views/my-finance/my-declarations/MyDeclarations.vue"
          ),
        meta: {
          title: "My Declarations",
          requiresAuth: true,
        },
      },
      {
        path: "/my-finance/my-declarations/fbp-declaration",
        name: "FBP Declaration",
        component: () =>
          import(
            /* webpackChunkName: "FBP Declaration" */ "../views/my-finance/my-declarations/fbp-declarations/FbpDeclaration.vue"
          ),
        meta: {
          title: "FBP Declaration",
          requiresAuth: true,
        },
      },
      {
        path: "/my-finance/travel-and-expenses/",
        name: "Travel and Expenses - Employee",
        component: () =>
          import(
            /* webpackChunkName: "Travel and Expenses - Employee" */ "../views/my-finance/travel-and-expenses/TravelAndExpenses.vue"
          ),
        meta: {
          title: "Travel and Expenses - Employee",
          requiresAuth: true,
        },
      },
      {
        path: "my-finance/travel-and-expenses/travel-request",
        name: "Travel Request - Employee",
        component: () =>
          import(
            /* webpackChunkName: "Travel Request - Employee" */ "../views/my-finance/travel-and-expenses/travel-request/EmployeeTravel.vue"
          ),
        meta: {
          title: "Travel Request - Employee",
          requiresAuth: true,
        },
      },
      {
        path: "/my-finance/travel-and-expenses/claim-request",
        name: "Claim Request",
        component: () =>
          import(
            /* webpackChunkName: "Claim Request" */ "../views/my-finance/travel-and-expenses/claim-request/ReimbursementRequest.vue"
          ),
        meta: {
          title: "Claim Request",
          requiresAuth: true,
        },
      },
      {
        path: "/my-finance/my-pay",
        name: "My Pay",
        component: () =>
          import(
            /* webpackChunkName: "My Pay" */ "../views/my-finance/my-pay/MyPay.vue"
          ),
        meta: {
          title: "My Pay",
          requiresAuth: true,
        },
      },
      {
        path: "/my-finance/my-pay/my-salary",
        name: "My Salary",
        component: () =>
          import(
            /* webpackChunkName: "My Salary" */ "../views/my-finance/my-pay/my-salary/MySalary.vue"
          ),
        meta: {
          title: "My Salary",
          requiresAuth: true,
        },
      },
      {
        path: "/my-finance/my-pay/my-payslip",
        name: "My Payslip",
        component: () =>
          import(
            /* webpackChunkName: "My Payslip" */ "../views/my-finance/my-pay/my-payslips/MyPayslips.vue"
          ),
        meta: {
          title: "My Payslip",
          requiresAuth: true,
        },
      },
      // ===============
      // core-hr
      // ===============
      {
        path: "/core-hr/document-subtype",
        name: "Document Subtype",
        component: () =>
          import(
            /* webpackChunkName: "Document Subtype" */ "../views/coreHr/employee-data-management/document-subtype/DocumentSubtype.vue"
          ),
        meta: {
          title: "Document Subtype",
          requiresAuth: true,
        },
      },
      {
        path: "/core-hr/accreditation-category-and-type",
        name: "Accreditation Category and Type",
        component: () =>
          import(
            /* webpackChunkName: "Accreditation Category and Type" */ "../views/coreHr/employee-data-management/accreditation-category-and-type/AccreditationCategoryAndType.vue"
          ),
        meta: {
          title: "Accreditation Category and Type",
          requiresAuth: true,
        },
      },
      {
        path: "/core-hr/time-off-management/time-off-closure",
        name: "Time Off Closure",
        component: () =>
          import(
            /* webpackChunkName: "Time Off Closure" */ "../views/coreHr/time-off-management/time-off-closure/TimeoffClosure.vue"
          ),
        meta: {
          title: "Time Off Closure",
          requiresAuth: true,
        },
      },
      {
        path: "/core-hr/business-unit",
        name: "Business Unit / Cost Center",
        component: () =>
          import(
            /* webpackChunkName: "Business Unit" */ "../views/coreHr/business-unit/BusinessUnit.vue"
          ),
        meta: {
          title: "Business Unit / Cost Center",
          requiresAuth: true,
        },
      },
      {
        path: "/core-hr/projects",
        name: "Projects",
        component: () =>
          import(
            /* webpackChunkName: "Projects" */ "../views/coreHr/employee-data-management/projects/ProjectMain.vue"
          ),
        meta: {
          title: "Projects",
          requiresAuth: true,
        },
      },
      {
        path: "/core-hr/department-hierarchy",
        name: "Department Hierarchy",
        component: () =>
          import(
            /* webpackChunkName: "Department Hierarchy" */ "../views/coreHr/department-hierarchy/DepartmentHierarchy.vue"
          ),
        meta: {
          title: "Department Hierarchy",
          requiresAuth: true,
        },
      },
      {
        path: "/core-hr/job-roles",
        name: "Job Roles",
        component: () =>
          import(
            /* webpackChunkName: "Job Roles" */ "../views/coreHr/job-roles/JobRoles.vue"
          ),
        meta: {
          title: "Job Roles",
          requiresAuth: true,
        },
      },
      {
        path: "/core-hr/time-off-management",
        name: "Time off Management",
        component: () =>
          import(
            /* webpackChunkName: "Time off Management" */ "../views/coreHr/time-off-management/TimeOffMangement.vue"
          ),
        meta: {
          title: "Time off Management",
          requiresAuth: true,
        },
      },
      {
        path: "/core-hr/time-off-management/leave-override",
        name: "Leave Override",
        component: () =>
          import(
            /* webpackChunkName: " Leave Override" */ "../views/coreHr/time-off-management/leave-override/LeaveOverride.vue"
          ),
        meta: {
          title: "Leave Override",
          requiresAuth: true,
        },
      },
      {
        path: "/core-hr/leave-policy",
        name: "Leave Policy",
        component: () =>
          import(
            /* webpackChunkName: "Leave Policy" */ "../views/coreHr/time-off-management/leave-policy/LeavePolicy.vue"
          ),
        meta: {
          title: "Leave Policy",
          requiresAuth: true,
        },
      },
      {
        path: "/core-hr/leave-closure",
        name: "Leave Closure",
        component: () =>
          import(
            /* webpackChunkName: "Leave Closure" */ "../views/coreHr/time-off-management/leave-closure/LeaveClosure.vue"
          ),
        meta: {
          title: "Leave Closure",
          requiresAuth: true,
        },
      },
      {
        path: "/core-hr/organization-group",
        name: "Organization Group",
        component: () =>
          import(
            /* webpackChunkName: "Organization Group" */ "../views/coreHr/organisation-group/OrganisationGroup.vue"
          ),
        meta: {
          title: "Organization Group",
          requiresAuth: true,
        },
      },

      // ===============
      // payroll
      // ===============
      {
        path: "/payroll/electronic-fund-transfer",
        name: "Electronic Fund Transfer",
        component: () =>
          import(
            /* webpackChunkName: "Electronic Fund Transfer" */ "../views/payroll/electronic-fund-transfer/ElectronicFundTransfer.vue"
          ),
        meta: {
          title: "Electronic Fund Transfer",
          requiresAuth: true,
        },
      },
      {
        path: "/payroll/salary-payslip/bi-monthly",
        name: "Bi-Monthly Salary Payslip",
        component: () =>
          import(
            /* webpackChunkName: "Bi-Monthly Salary Payslip" */ "../views/payroll/salary-payslip/SalaryPayslip.vue"
          ),
        meta: {
          title: "Bi-Monthly Salary Payslip",
          requiresAuth: true,
        },
      },
      {
        path: "/payroll/bonus",
        name: "Bonus Import",
        component: () =>
          import(
            /* webpackChunkName: "Bonus Import" */ "../views/payroll/bonus-import/BonusImport.vue"
          ),
        meta: {
          title: "Bonus Import",
          requiresAuth: true,
        },
      },
      {
        path: "/payroll/tds-history",
        name: "TDS History Import",
        component: () =>
          import(
            /* webpackChunkName: "TDS History Import" */ "../views/payroll/tds-history-import/TdsHistoryImport.vue"
          ),
        meta: {
          title: "TDS History Import",
          requiresAuth: true,
        },
      },
      {
        path: "/payroll/full-and-final-settlement",
        name: "Full & Final Settlement",
        component: () =>
          import(
            /* webpackChunkName: "Full & Final Settlement" */ "../views/payroll/full-and-final-settlement/FullAndFinalSettlement.vue"
          ),
        meta: {
          title: "Full & Final Settlement",
          requiresAuth: true,
        },
      },
      {
        path: "/payroll/payroll-reconciliation",
        name: "Payroll Reconciliation",
        component: () =>
          import(
            /* webpackChunkName: "Payroll Reconciliation" */ "../views/payroll/payroll-reconciliation/PayrollReconciliation.vue"
          ),
        meta: {
          title: "Payroll Reconciliation",
          requiresAuth: true,
        },
      },

      // =================
      // employee-profile
      // =================
      {
        path: "/employee-self-service/my-profile",
        name: "Employee Profile",
        component: () =>
          import(
            /* webpackChunkName: "Employee Profile" */ "../views/employee-profile/EmployeeProfile.vue"
          ),
        meta: {
          title: "Employee Profile",
          requiresAuth: true,
        },
      },

      // ===============
      // my-team
      // ===============
      {
        path: "/my-team/attendance",
        name: "Attendance-Admin",
        component: () =>
          import(
            /* webpackChunkName: " Attendance-Admin" */ "../views/coreHr/time-off-management/employee-attendance/AttendanceCalling.vue"
          ),
        meta: {
          title: "Attendance-Admin",
          requiresAuth: true,
        },
      },
      {
        path: "/my-team/attendance/attendance-approvals",
        name: "Approvals",
        component: () =>
          import(
            /* webpackChunkName: "Approvals" */ "../views/coreHr/time-off-management/employee-attendance/AttendanceApproval.vue"
          ),
        meta: {
          title: "Approvals",
          requiresAuth: true,
        },
      },
      {
        path: "/my-team/attendance/attendance-bulk-action",
        name: "Attendance Bulk Action",
        component: () =>
          import(
            /* webpackChunkName: "Attendance Bulk Action" */ "../views/my-team/attendance/attendance-bulk-action/AttendanceDelete.vue"
          ),
        meta: {
          title: "Attendance Bulk Action",
          requiresAuth: true,
        },
      },
      {
        path: "/my-team/attendance/attendance-finalization",
        name: "Attendance Finalization",
        component: () =>
          import(
            /* webpackChunkName: "Attendance Finalization" */ "../views/my-team/attendance/attendance-finalization/AttendanceFinalization.vue"
          ),
        meta: {
          title: "Attendance Finalization",
          requiresAuth: true,
        },
      },
      {
        path: "/my-team/early-checkout",
        name: "Early Checkout",
        component: () =>
          import(
            /* webpackChunkName: "Early Checkout" */ "../views/my-team/attendance/early-checkout/EarlyCheckout.vue"
          ),
        meta: {
          title: "Early Checkout",
          requiresAuth: true,
        },
      },
      {
        path: "/my-team/compensatory-off",
        name: "Compensatory Off",
        component: () =>
          import(
            /* webpackChunkName: "Compensatory Off" */ "../views/my-team/time-off/comp-off/CompOff.vue"
          ),
        meta: {
          title: "Compensatory Off",
          requiresAuth: true,
        },
      },
      {
        path: "/my-team/travel-and-expenses",
        name: "Travel and Expenses - Admin",
        component: () =>
          import(
            /* webpackChunkName: "Travel and Expenses - Admin" */ "../views/my-team/travel-requests/TravelAndExpenses.vue"
          ),
        meta: {
          title: "Travel and Expenses - Admin",
          requiresAuth: true,
        },
      },
      {
        path: "/my-team/travel-and-expenses/claim-request",
        name: "Claim Request - Admin",
        component: () =>
          import(
            /* webpackChunkName: "Claim Request - Admin" */ "../views/my-team/travel-requests/AdminReimbursement.vue"
          ),
        meta: {
          title: "Claim Request - Admin",
          requiresAuth: true,
        },
      },
      {
        path: "/employee-self-service/attendance",
        name: "Attendance-Employee",
        component: () =>
          import(
            /* webpackChunkName: " Attendance-Employee" */ "../views/coreHr/time-off-management/employee-attendance/AdminAttendance.vue"
          ),
        meta: {
          title: "Attendance-Employee",
          requiresAuth: true,
        },
      },
      {
        path: "/my-team/team-summary",
        name: "Team Summary",
        component: () =>
          import(
            /* webpackChunkName: "Team Summary" */ "../views/my-team/team-summary/TeamSummary.vue"
          ),
        meta: {
          title: "Team Summary",
          requiresAuth: true,
        },
      },
      {
        path: "/my-team/team-summary/reports",
        name: "Reports",
        component: () =>
          import(
            /* webpackChunkName: "Reports" */ "../views/my-team/team-summary/team-sumary-reports/TeamSummaryReports.vue"
          ),
        meta: {
          title: "Reports",
          requiresAuth: true,
        },
      },
      {
        path: "/my-team/team-summary/:employeeId/:tabName",
        name: "Team Summary - External",
        component: () =>
          import(
            /* webpackChunkName: "Team Summary - External" */ "../views/my-team/team-summary/TeamSummary.vue"
          ),
        meta: {
          title: "Team Summary",
          requiresAuth: true,
        },
      },
      // ===============
      // My Team -> time-off
      // ===============
      {
        path: "/my-team/time-off",
        name: "Time Off",
        component: () =>
          import(
            /* webpackChunkName: "time-off" */ "../views/my-team/time-off/TimeOffSetup.vue"
          ),
        meta: {
          title: "Time Off",
          requiresAuth: true,
        },
      },
      {
        path: "/my-team/leave-request",
        name: "Leave Request",
        component: () =>
          import(
            /* webpackChunkName: "Leave Request" */ "../views/my-team/time-off/leaves/MyTeamLeavesSetup.vue"
          ),
        meta: {
          title: "Leave Request",
          requiresAuth: true,
        },
      },
      {
        path: "/my-team/short-time-off",
        name: "Short Time Off",
        component: () =>
          import(
            /* webpackChunkName: "Short Time Off" */ "../views/my-team/time-off/short-time-off/short-time-off-request/ShortTimeOffCalling.vue"
          ),
        meta: {
          title: "Short Time Off",
          requiresAuth: true,
        },
      },
      {
        path: "/my-team/reports",
        name: "Reports ",
        component: () =>
          import(
            /* webpackChunkName: "Reports" */ "../views/my-team/time-off/time-off-reports/TimeOffReports.vue"
          ),
        meta: {
          title: "Reports",
          requiresAuth: true,
        },
      },
      // ===============
      // Employee Self Service -> time-off
      // ===============
      {
        path: "/employee-self-service/time-off",
        name: "Time Off ",
        component: () =>
          import(
            /* webpackChunkName: "time-off" */ "../views/employee-self-service/time-off/TimeOffSetup.vue"
          ),
        meta: {
          title: "Time Off ",
          requiresAuth: true,
        },
      },
      {
        path: "/employee-self-service/leave-request",
        name: "Leave Request ",
        component: () =>
          import(
            /* webpackChunkName: "Leave Request" */ "../views/employee-self-service/time-off/leaves/SelfServiceLeavesSetup.vue"
          ),
        meta: {
          title: "Leave Request ",
          requiresAuth: true,
        },
      },
      {
        path: "/employee-self-service/compensatory-off",
        name: "Compensatory Off ",
        component: () =>
          import(
            /* webpackChunkName: "Compensatory Off" */ "../views/employee-self-service/time-off/comp-off/CompOff.vue"
          ),
        meta: {
          title: "Compensatory Off",
          requiresAuth: true,
        },
      },
      {
        path: "/employee-self-service/short-time-off",
        name: "Short Time Off ",
        component: () =>
          import(
            /* webpackChunkName: "Short Time Off " */ "../views/employee-self-service/time-off/short-time-off/SelfServiceShortTimeOffSetUp.vue"
          ),
        meta: {
          title: "Short Time Off  ",
          requiresAuth: true,
        },
      },
      {
        path: "/recruitment/job-candidates/:candidateId",
        name: "Job Candidates - Details",
        component: () =>
          import(
            /* webpackChunkName: "job-candidates-details" */ "../views/recruitment/job-candidates/JobCandidates.vue"
          ),
        meta: {
          title: "Job Candidates - Details",
          requiresAuth: true,
        },
      },
      {
        path: "/recruitment/job-candidates/duplicate-candidates/:candidateId",
        name: "Duplicate Job Candidates - Details",
        component: () =>
          import(
            /* webpackChunkName: "duplicate job-candidates-details" */ "../views/recruitment/job-candidates/DuplicateJobCandidates.vue"
          ),
        meta: {
          title: " Duplicate Job Candidates - Details",
          requiresAuth: true,
        },
      },
      {
        path: "/recruitment/job-candidates/archived-candidates/:candidateId",
        name: "Archived Candidates - Details",
        component: () =>
          import(
            /* webpackChunkName: "archived job-candidates-details" */ "../views/recruitment/job-candidates/DuplicateJobCandidates.vue"
          ),
        meta: {
          title: "Archived Job Candidates - Details",
          requiresAuth: true,
        },
      },
      {
        path: "/recruitment/job-candidates/talent-pool/:candidateId",
        name: "Talent Pool - Details",
        component: () =>
          import(
            /* webpackChunkName: "talent pool-details" */ "../views/recruitment/job-candidates/onboarding/talent-pool/TalentPool.vue"
          ),
        meta: {
          title: "Talent Pool - Details",
          requiresAuth: true,
        },
      },
      {
        path: "my-team/lop-recovery",
        name: "LOP Recovery - Team",
        component: () =>
          import(
            /* webpackChunkName: "LOP Recovery - Team" */ "../views/my-team/lop-recovery/LopRecoveryMainForm.vue"
          ),
        meta: {
          title: "LOP Recovery - Team",
          requiresAuth: true,
        },
      },
      {
        path: "my-team/compensatory-off-balance",
        name: "Comp Off Balance - Team",
        component: () =>
          import(
            /* webpackChunkName: "Comp Off Balance - Team" */ "../views/my-team/comp-off-balance/CompOffBalanceMain.vue"
          ),
        meta: {
          title: "Comp Off Balance - Team",
          requiresAuth: true,
        },
      },
      {
        path: "my-team/pre-approval",
        name: "Pre Approval - Team",
        component: () =>
          import(
            /* webpackChunkName: "Pre Approval - Team" */ "../views/my-team/pre-approval/PreApproval"
          ),
        meta: {
          title: "Pre Approval - Team",
          requiresAuth: true,
        },
      },
      {
        path: "my-team/travel-and-expenses/travel-request",
        name: "Travel Request - Team",
        component: () =>
          import(
            /* webpackChunkName: "Travel Request - Team" */ "../views/my-team/travel-requests/TravelRequest.vue"
          ),
        meta: {
          title: "Travel Request - Team",
          requiresAuth: true,
        },
      },
      {
        path: "/my-team/timesheets",
        name: "Timesheets - Team",
        component: () =>
          import(
            /* webpackChunkName: "Timesheets - Team" */ "../views/my-team/timesheets/Timesheets.vue"
          ),
        meta: {
          title: "Timesheets - Team",
          requiresAuth: true,
        },
      },
      {
        path: "/my-team/exit-management",
        name: "Exit Management",
        component: () =>
          import(
            /* webpackChunkName: "Exit Management" */ "../views/my-team/exit-management/ExitManagement.vue"
          ),
        meta: {
          title: "Exit Management",
          requiresAuth: true,
        },
      },
      {
        path: "/my-team/uniform-monitoring",
        name: "Uniform Monitoring",
        component: () =>
          import(
            /* webpackChunkName: "Uniform Monitoring" */ "../views/my-team/uniform-monitoring/UniformMonitoring.vue"
          ),
        meta: {
          title: "Uniform Monitoring",
          requiresAuth: true,
        },
      },
      // ===============
      // Tax and Statutory Compliance
      // ===============
      {
        path: "/tax-and-statutory-compliance/statutory-components",
        name: "Statutory Components",
        component: () =>
          import(
            /* webpackChunkName: "Statutory Components" */ "../views/tax-and-statutory-compliance/statutory-components/StatutoryComponents.vue"
          ),
        meta: {
          title: "Statutory Components",
          requiresAuth: true,
        },
      },
      {
        path: "/tax-and-statutory-compliance/statutory-components/provident-fund",
        name: "Provident Fund",
        component: () =>
          import(
            /* webpackChunkName: "Provident Fund" */ "../views/tax-and-statutory-compliance/statutory-components/provident-fund/ProvidentFund.vue"
          ),
        meta: {
          title: "Provident Fund",
          requiresAuth: true,
        },
      },
      {
        path: "/tax-and-statutory-compliance/statutory-components/nps",
        name: "NPS",
        component: () =>
          import(
            /* webpackChunkName: "NPS" */ "../views/tax-and-statutory-compliance/statutory-components/nps/NPS.vue"
          ),
        meta: {
          title: "NPS",
          requiresAuth: true,
        },
      },
      {
        path: "/tax-and-statutory-compliance/statutory-components/insurance",
        name: "Insurance",
        component: () =>
          import(
            /* webpackChunkName: "Insurance" */ "../views/tax-and-statutory-compliance/statutory-components/insurance/InsuranceForm.vue"
          ),
        meta: {
          title: "Insurance",
          requiresAuth: true,
        },
      },
      {
        path: "/tax-and-statutory-compliance/compliance-forms/form-24q",
        name: "Form 24Q",
        component: () =>
          import(
            /* webpackChunkName: "Form 24Q" */ "../views/tax-and-statutory-compliance/compliance-forms/form-24q/Form24Q.vue"
          ),
        meta: {
          title: "Form 24Q",
          requiresAuth: true,
        },
      },
      {
        path: "/tax-and-statutory-compliance/tax-relief",
        name: "Tax Relief",
        component: () =>
          import(
            /* webpackChunkName: "Tax Relief" */ "../views/tax-and-statutory-compliance/tax-relief/TaxRelief.vue"
          ),
        meta: {
          title: "Tax Relief",
          requiresAuth: true,
        },
      },
      // ===============
      // Recruitment
      // ===============
      {
        path: "/recruitment/dashboard",
        name: "Recruitment DashBoard",
        component: () =>
          import(
            /* webpackChunkName: "Recruitment Dashboard" */ "../views/recruitment/RecruitmentDashboard.vue"
          ),
        meta: {
          title: "Dashboard",
          requiresAuth: true,
        },
      },
      {
        path: "/recruitment/job-candidates",
        name: "Job Candidates",
        component: () =>
          import(
            /* webpackChunkName: "Job Candidates" */ "../views/recruitment/job-candidates/JobCandidates.vue"
          ),
        meta: {
          title: "Job Candidates",
          requiresAuth: true,
        },
      },
      {
        path: "/recruitment/job-candidates/duplicate-candidates",
        name: "Duplicate Job Candidates",
        component: () =>
          import(
            /* webpackChunkName: "Duplicate Job Candidates" */ "../views/recruitment/job-candidates/DuplicateJobCandidates.vue"
          ),
        meta: {
          title: "Duplicate Candidates",
          requiresAuth: true,
        },
      },
      {
        path: "/recruitment/job-candidates/archived-candidates",
        name: "Archived Job Candidates",
        component: () =>
          import(
            /* webpackChunkName: "Archived Job Candidates" */ "../views/recruitment/job-candidates/ArchivedJobCandidates.vue"
          ),
        meta: {
          title: "Archived Candidates",
          requiresAuth: true,
        },
      },
      {
        path: "/recruitment/job-candidates/onboarding",
        name: "Job Candidates-onboarding",
        component: () =>
          import(
            /* webpackChunkName: "Job Candidates-onboarding" */ "../views/recruitment/job-candidates/onboarding/JobCandidateOnboarding.vue"
          ),
        meta: {
          title: "Job Candidates-onboarding",
          requiresAuth: true,
        },
      },
      {
        path: "/recruitment/job-candidates/upcoming-interviews",
        name: "Job Candidates-upcoming-interviews",
        component: () =>
          import(
            /* webpackChunkName: "Job Candidates-Upcoming-Interviews" */ "../views/recruitment/job-candidates/upcoming-interviews/UpcomingInterviews.vue"
          ),
        meta: {
          title: "Upcoming Interviews",
          requiresAuth: true,
        },
      },
      {
        path: "/recruitment/job-candidates/pending-feedback",
        name: "Job Candidates-pending-feedback",
        component: () =>
          import(
            /* webpackChunkName: "Job Candidates-pending-feedback" */ "../views/recruitment/job-candidates/pending-feedback/PendingFeedback.vue"
          ),
        meta: {
          title: "Pending Feedbacks",
          requiresAuth: true,
        },
      },
      {
        path: "/recruitment/job-posts",
        name: "Job Posts",
        component: () =>
          import(
            /* webpackChunkName: "Job Posts" */ "../views/settings/integration/JobPostPage.vue"
          ),
        meta: {
          title: "Job Posts",
          requiresAuth: true,
        },
      },
      {
        path: "/recruitment/job-candidates/talent-pool",
        name: "Job Candidates-talent-pool",
        component: () =>
          import(
            /* webpackChunkName: "Job Candidates-talent-pool" */ "../views/recruitment/job-candidates/onboarding/talent-pool/TalentPool.vue"
          ),
        meta: {
          title: "Talent Pool",
          requiresAuth: true,
        },
      },
      {
        path: "/recruitment/interview-rounds-master",
        name: "Interview Rounds Master",
        component: () =>
          import(
            /* webpackChunkName: "Interview Rounds Master" */ "../views/recruitment/interview-round-master/InterviewRoundMaster.vue"
          ),
        meta: {
          title: "Interview Rounds Master",
          requiresAuth: true,
        },
      },
      {
        path: "/recruitment/my-integration",
        name: "My Integration",
        component: () =>
          import(
            /* webpackChunkName: "My Integration" */ "../views/recruitment/my-integrations/MyIntegrations.vue"
          ),
        meta: {
          title: "My Integration",
          requiresAuth: true,
        },
      },
      // ===============
      // Data-Loss-Prevention
      // ===============
      {
        path: "/data-loss-prevention/location-intelligence",
        name: "Location Intelligence",
        component: () =>
          import(
            /* webpackChunkName: "Location Intelligence" */ "../views/data-loss-prevention/location-intelligence/UserActivity.vue"
          ),
        meta: {
          title: "Location Intelligence",
          requiresAuth: true,
        },
      },
      {
        path: "/data-loss-prevention/key-logger",
        name: "Key Logger",
        component: () =>
          import(
            /* webpackChunkName: "Key Logger" */ "../views/data-loss-prevention/key-logger/MainKeyLogs.vue"
          ),
        meta: {
          title: "Key Logger",
          requiresAuth: true,
        },
      },
      // ===============
      // Productivity-Monitoring
      // ===============
      {
        path: "/productivity-monitoring/activity-dashboard/cxo-dashboard",
        name: "CXO Dashboard",
        component: () =>
          import(
            /* webpackChunkName: "CXO Dashboard" */ "../views/productivity-monitoring/activity-dashboard/cxo-dashboard/CXODashboard.vue"
          ),
        meta: {
          title: "CXO Dashboard",
          requiresAuth: true,
        },
      },
      // ===============
      // Man-Power-Planning
      // ===============
      {
        path: "/man-power-planning/settings",
        name: "Man Power Planning",
        component: () =>
          import(
            /* webpackChunkName: "Man Power Planning" */ "../views/settings/hiring-forecast/HiringForecast.vue"
          ),
        meta: {
          title: "Man Power Planning",
          requiresAuth: true,
        },
      },
      {
        path: "/man-power-planning/settings/general",
        name: "Man Power Planning ",
        component: () =>
          import(
            /* webpackChunkName: "Man Power Planning" */ "../views/settings/hiring-forecast/MPPGeneral.vue"
          ),
        meta: {
          title: "Man Power Planning",
          requiresAuth: true,
        },
      },
      {
        path: "/man-power-planning/hiring-forecast",
        name: "Hiring Forecast",
        component: () =>
          import(
            /* webpackChunkName: "Hiring Forecast" */ "../views/man-power-planning/hiring-forecast/HiringForecast.vue"
          ),
        meta: {
          title: "Hiring Forecast",
          requiresAuth: true,
        },
      },
      {
        path: "/man-power-planning/table-of-organization",
        name: "Table Of Organization",
        component: () =>
          import(
            /* webpackChunkName: "Table Of Organization" */ "../views/man-power-planning/table-of-organization/TableOfOrganization.vue"
          ),
        meta: {
          title: "Table Of Organization",
          requiresAuth: true,
        },
      },
      {
        path: "/man-power-planning/job-requisition",
        name: "Job Requisition",
        component: () =>
          import(
            /* webpackChunkName: "Job Requisition" */ "../views/man-power-planning/job-requisition/JobRequisition.vue"
          ),
        meta: {
          title: "Job Requisition",
          requiresAuth: true,
        },
      },
      // ===============
      // Reports
      // ===============
      {
        path: "/form-generator",
        name: "Form Generator",
        component: () =>
          import(
            /* webpackChunkName: "form-generator" */ "../views/reports/forms-generator/FormGenerator.vue"
          ),
        meta: {
          title: "Reports",
          requiresAuth: true,
        },
      },
      // ===============
      // Roster Management
      // ===============
      {
        path: "/roster-management/shift-rotation",
        name: "Shift Rotation",
        component: () =>
          import(
            /* webpackChunkName: "shift-rotation" */ "../views/roster-management/shift-rotation/ShiftRotation.vue"
          ),
        meta: {
          title: "Shift Rotation",
          requiresAuth: true,
        },
      },
      // ===============
      // Roster Management
      // ===============
      {
        path: "/roster-management/shift-swap",
        name: "Shift Swap",
        component: () =>
          import(
            /* webpackChunkName: "shift-swap" */ "../views/roster-management/shift-swap/MyShiftSwap.vue"
          ),
        meta: {
          title: "Shift Swap",
          requiresAuth: true,
        },
      },
      {
        path: "/roster-management/shift-swap-approvals",
        name: "Shift Swap Approvals",
        component: () =>
          import(
            /* webpackChunkName: "shift-swap-approvals" */ "../views/roster-management/shift-swap-approval/ShiftSwapApproval.vue"
          ),
        meta: {
          title: "Shift Swap Approvals",
          requiresAuth: true,
        },
      },
      {
        path: "/job-post",
        name: "Job Post Details",
        component: () =>
          import(
            /* webpackChunkName: "job-post details" */ "../views/pages/JobPostDetails.vue"
          ),
        meta: {
          title: "Job Post Details",
          requiresAuth: true,
        },
      },
      // ===============
      // Compliance Management
      // ===============
      {
        path: "/compliance-management/docusign/configuration",
        name: "DocuSign Configuration",
        component: () =>
          import(
            /* webpackChunkName: "DocuSign Configuration" */ "../views/compliance-management/docusign/ComplianceManagement.vue"
          ),
        meta: {
          title: "DocuSign Configuration",
          requiresAuth: true,
        },
      },
         {
        path: "/compliance-management/docusign",
        name: "DocuSign",
        component: () =>
          import(
            /* webpackChunkName: "docusign" */ "../views/compliance-management/docusign/ComplianceManagement.vue"
          ),
        meta: {
          title: "DocuSign",
          requiresAuth: true,
        },
      },

    ],
  },

  // unAuthenticated routing
  {
    path: "",
    component: () => import("@/layouts/UnAuthLayout.vue"),
    children: [
      {
        path: "/job-candidates",
        name: "Job Candidates - Public",
        component: () =>
          import(
            /* webpackChunkName: "job-candidates-public" */ "../views/pages/JobCandidatesPublicForm.vue"
          ),
        meta: {
          title: "Job Candidates - Public",
          requiresAuth: false,
        },
      },
      {
        path: "/careers",
        name: "Careers",
        component: () =>
          import(
            /* webpackChunkName: "careers-public" */ "../views/pages/careers/CareerPage.vue"
          ),
        meta: {
          title: "Careers",
          requiresAuth: false,
        },
      },
      {
        path: "/candidate-portal",
        name: "Candidate Portal - Public",
        component: () =>
          import(
            /* webpackChunkName: "candidate-portal-public" */ "../views/pages/candidate-portal/CandidateSiginPortal.vue"
          ),
        meta: {
          title: "Candidate Portal - Public",
          requiresAuth: false,
        },
      },
      // ===============
      // Entomo Auth
      // ===============
      {
        path: "/entomohr",
        name: "Entomo Auth",
        component: () =>
          import(
            /* webpackChunkName: "Entomo Auth" */ "../views/pages/EntomoLandingScreen.vue"
          ),
        meta: {
          title: "Entomo Auth",
          requiresAuth: false,
        },
      },
    ],
  },

  {
    path: "/page-not-found",
    name: "PageNotFound",
    component: () => import("../views/pages/PageNotFound.vue"),
    meta: {
      title: "Page Not Found",
    },
  },

  // Redirect to 404 page, if no match found
  {
    path: "/:pathMatch(.*)*",
    redirect: "/page-not-found",
  },
];

const router = createRouter({
  history: createWebHistory(process.env.BASE_URL),
  routes,
});

router.afterEach(() => {
  // Remove initial loading
  const appLoading = document.getElementById("loading-bg");
  if (appLoading) {
    appLoading.style.display = "none";
  }
});

router.beforeEach((guard) => {
  firebase.auth().onAuthStateChanged(() => {
    const { meta, matched } = guard;
    let org_name = store.getters.organizationName;

    let domain = store.getters.domain;
    let title = localStorage.getItem("pageTitle");
    if (domain === "cannyhr") {
      //To Update App Title for cannyhr domain
      if (org_name) {
        document.title =
          meta.title + " - " + org_name + " | " + (title ? title : "CANNYHR");
      } else {
        document.title = meta.title + " | " + (title ? title : "CANNYHR");
      }
    } else {
      if (org_name) {
        document.title =
          meta.title + " - " + org_name + " | " + (title ? title : "HRAPP");
      } else {
        //incase any error while receiving company name present the below title
        document.title =
          meta.title + " | " + (title ? title : "HRAPP - Hire to Retire");
      }
    }

    // get firebase current user
    const firebaseCurrentUser = firebase.auth().currentUser;
    if (firebaseCurrentUser) {
      //note: if firebase user exist we store the user data in store for future use.
      firebase
        .auth()
        .currentUser.getIdTokenResult()
        .then((signedUserData) => {
          store.commit("UPDATE_USER_INFO", signedUserData);
        })
        .catch(() => {
          store.commit("UPDATE_USER_INFO", {});
        });
    }
    const requiresAuth = matched.some((record) => record.meta.requiresAuth);
    if (requiresAuth) {
      const accessToken = window.$cookies.get("accessToken");
      const refreshToken = window.$cookies.get("refreshToken");
      //when user sign-in from mobile we don't get refresh-token  in cookie as of now
      //so if either refresh-token or native-app we allow user to enter into our app
      if (accessToken || refreshToken) {
        return true;
      } else {
        let baseUrl = store.getters.baseUrl;
        store.dispatch("clearUserLock");
        return (window.location.href = baseUrl + "auth");
      }
    } else {
      return true;
    }
  });
});

router.onError(() => {
  // const isChunkLoadFailed = err.message.includes("Loading", "failed");
  // if (isChunkLoadFailed) {
  //   return window.location.reload();
  // } else {
  const accessToken = window.$cookies.get("accessToken");
  const refreshToken = window.$cookies.get("refreshToken");
  if (!accessToken && !refreshToken) {
    let baseUrl = store.getters.baseUrl;
    //redirect to auth page if no access token or refresh token
    return (window.location.href = baseUrl + "auth");
  }
  // }
});

export default router;
