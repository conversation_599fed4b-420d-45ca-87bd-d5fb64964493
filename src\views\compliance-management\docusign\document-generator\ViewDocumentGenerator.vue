<template>
  <div>
    <v-card min-height="500" class="card-radius">
      <div
        class="d-flex align-center"
        style="justify-content: space-between; border-bottom: 2px solid #e5e5e5"
      >
        <div class="d-flex align-center primary--text font-weight-medium pl-4">
          <v-avatar class="mr-2" size="35" color="grey lighten-3">
            <v-icon size="25" color="grey"> description </v-icon>
          </v-avatar>
          <div
            class="text-truncate"
            :style="isMobileView ? 'max-width: 150px' : 'max-width: 250px'"
          >
            {{ docGeneratorDetails.documentName }}
          </div>
        </div>
        <div class="pa-3 d-flex">
          <v-btn
            v-if="
              formAccess &&
              formAccess.update &&
              docGeneratorDetails.status === 'Draft' &&
              !isMobileView
            "
            color="secondary"
            dense
            small
            class="mr-1"
            @click="$emit('open-edit-form')"
          >
            Edit
          </v-btn>
          <v-icon color="grey" @click="$emit('close-view-form')">
            close
          </v-icon>
        </div>
      </div>
      <v-card-text>
        <v-row>
          <v-col cols="12" sm="6" class="px-md-12">
            <div class="primary--text body-1 font-weight-medium">
              Employee Name
            </div>
            <div class="grey--text body-1 mt-4">
              {{ checkNullValue(docGeneratorDetails.employeeName) }}
            </div>
          </v-col>
          <v-col cols="12" sm="6" class="px-md-12">
            <div class="primary--text body-1 font-weight-medium">
              Candidate Name
            </div>
            <div class="grey--text body-1 mt-4">
              {{ checkNullValue(docGeneratorDetails.candidateName) }}
            </div>
          </v-col>
          <v-col cols="12" class="px-md-12">
            <div class="primary--text body-1 font-weight-medium">
              Document Content
            </div>
            <div id="docContent" class="mt-4 ck-content" />
          </v-col>
        </v-row>
      </v-card-text>
    </v-card>
  </div>
</template>

<script>
import { checkNullValue } from "@/helper";

export default {
  name: "ViewDocumentGenerator",

  props: {
    docGeneratorDetails: {
      type: Object,
      required: true,
    },
    formAccess: {
      type: [Object, Boolean],
      required: true,
    },
    docGeneratorContent: {
      type: String,
      default: "",
    },
  },

  computed: {
    isMobileView() {
      return this.$store.state.isMobileWindowSize;
    },
  },

  mounted() {
    let content = this.docGeneratorContent;
    let element = document.getElementById("docContent");
    element.innerHTML = content;
  },

  methods: { checkNullValue },
};
</script>
