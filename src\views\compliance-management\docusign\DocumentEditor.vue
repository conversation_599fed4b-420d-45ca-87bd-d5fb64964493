<template>
  <VuePerfectScrollbar class="scroll-area" :settings="scrollbarSettings">
    <div>
      <ckeditor
        v-if="isMounted"
        v-model="editorData"
        :editor="editor"
        @ready="onReady"
        @input="onEditDocumentEditor"
      />
    </div>
  </VuePerfectScrollbar>
</template>

<script>
import Vue from "vue";
import CKEditor from "@ckeditor/ckeditor5-vue2";
import BalloonBlockEditor from "../../../ckeditor";
Vue.use(CKEditor);

export default {
  name: "DocumentEditor",

  props: {
    editorContent: {
      type: String,
      default: "",
    },
    insertContent: {
      type: String,
      default: "",
    },
    htmlTemplateContent: {
      type: String,
      default: "",
    },
  },

  data: () => ({
    editor: BalloonBlockEditor,
    ckEditor: "",
    editorData: ``,
    isMounted: false,
    isPageBreakReplaced: false,
  }),

  computed: {
    scrollbarSettings() {
      return this.$store.state.scrollbarSettings;
    },
  },

  watch: {
    insertContent(content) {
      let insertedContent = content ? content.split("@") : content;
      insertedContent = insertedContent ? insertedContent[0] : "";
      let position = this.ckEditor.model.document.selection.getLastPosition();
      this.ckEditor.model.change((writer) => {
        writer.insertText(insertedContent, position);
      });
    },
    htmlTemplateContent(template) {
      let content = template ? template.split("@") : template;
      content = content ? content[0] : "";
      const viewFragment = this.ckEditor.data.processor.toView(content);
      const modelFragment = this.ckEditor.data.toModel(viewFragment);
      this.ckEditor.model.insertContent(modelFragment);
    },
  },

  mounted() {
    if (this.editorContent) this.editorData = this.editorContent;
    this.isMounted = true;
  },

  methods: {
    onReady(editor) {
      this.ckEditor = editor;
      editor.focus();
    },
    onEditDocumentEditor() {
      // var count = (this.editorData.match(/page-break-after/g) || []).length;
      // if (!this.isPageBreakReplaced)
      //   this.editorData = this.editorData.replace(
      //     `<div class="page-break" style="page-break-after:always;"><span style="display:none;">&nbsp;</span></div>`,
      //     `<div class="page-break" style="page-break-after:always;"><span style="display:none;">&nbsp;</span></div><h2 style="text-align:right;"><strong><img class="image_resized" style="width:22.69%;" src="https://s3.ap-south-1.amazonaws.com/s3.hrapp-dev-public-images/Signature-Images/imagePlaceholder.png" alt="Organization Logo"></strong>&nbsp;</h2><h3 style="text-align:right;">{Organization Name}&nbsp;</h3><hr><p>&nbsp;</p><p>&nbsp;</p><p>&nbsp;</p><p>&nbsp;</p>`
      //   );
      // this.isPageBreakReplaced = true;
    },
  },
};
</script>

<style>
.ck.ck-editor {
  height: 100%;
}
.ck-editor__main {
  height: 100%;
}
.ck.ck-editor__editable_inline {
  height: 1123px;
  border: 1px solid grey !important;
  background: white;
  padding: 25px !important;
}
</style>
