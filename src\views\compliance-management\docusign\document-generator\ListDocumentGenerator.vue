<template>
  <div>
    <div
      v-if="pageCount > 0"
      class="d-flex flex-wrap align-center mb-4"
      :class="isMobileView ? 'mt-2 justify-center' : 'justify-end'"
    >
      <v-pagination
        id="doc_template_pagination"
        v-model="page"
        :length="pageCount"
        :total-visible="5"
        circle
      />
      <ButtonDropdown @selected-value="fnChangePaginationCount($event)" />
      <v-btn
        v-if="formAccess.add && windowWidth >= 1264"
        color="primary"
        class="font-weight-medium ml-2"
        :class="isMobileView ? 'mt-3 mb-n5' : 'mr-3'"
        @click="$emit('open-add-form')"
      >
        <v-icon style="color: white !important" class="pr-1"> add </v-icon>
        Add New
      </v-btn>
      <v-btn
        color="transparent"
        class="mx-1"
        text
        small
        @click="$emit('refetch-list')"
      >
        <v-icon color="grey"> refresh </v-icon>
      </v-btn>
    </div>
    <v-data-table
      id="doc_template_table"
      v-model="selectedRecords"
      :headers="tableHeaders"
      :items="tableItems"
      item-key="generatedDocumentId"
      hide-default-footer
      :items-per-page="itemsPerPage"
      :page.sync="page"
      :search="searchValue"
      :show-select="false"
      fixed-header
      :height="$store.getters.getTableHeight(230)"
      @page-count="pageCount = $event"
    >
      <!-- when there are no search data -->
      <template #no-results>
        <AppFetchErrorScreen
          key="no-results-screen"
          button-text="Clear All"
          icon-name="fas fa-sync"
          main-title="No matching search results found."
          content="Please try again by changing the filters or clear it to get all data."
          image-name="common/no-records"
          @button-click="clearFilter()"
        />
      </template>

      <template #[`header.employeeName`]="{}">
        <div class="docgen-custom-table-headers font-weight-medium body-2">
          Employee Name
        </div>
      </template>

      <template #[`header.candidateName`]="{}">
        <div class="docgen-custom-table-headers font-weight-medium body-2">
          Candidate Name
        </div>
      </template>

      <template #[`header.documentName`]="{}">
        <div class="docgen-custom-table-headers font-weight-medium body-2">
          Document Name
        </div>
      </template>

      <template #[`header.authorizedSignatories`]="{}">
        <div class="docgen-custom-table-headers font-weight-medium body-2">
          Signatories
        </div>
      </template>

      <template #[`header.status`]="{}">
        <div class="docgen-custom-table-headers font-weight-medium body-2">
          Status
        </div>
      </template>
      <template #[`header.action`]="{}">
        <div class="docgen-custom-table-headers font-weight-medium body-2">
          Action
        </div>
      </template>
      <template #[`header.documentAuthor`]="{}">
        <div class="docgen-custom-table-headers font-weight-medium body-2">
          Document Author
        </div>
      </template>

      <!-- table item slots -->
      <template #item="{ item }">
        <tr
          :id="'doc_template_list__tr_' + item.generatedDocumentId"
          class="data-table-tr cursor_pointer"
          :class="{ 'v-data-table__mobile-table-row': isMobileView }"
          @click="selectDocTemplateRecord(item)"
        >
          <!-- Employee Name -->
          <td :class="isMobileView ? 'v-data-table__mobile-row' : ''">
            <div v-if="isMobileView" class="v-data-table__mobile-row__header">
              Employee Name
            </div>
            <div :class="isMobileView ? 'v-data-table__mobile-row__cell' : ''">
              <section class="secondary--text body-2 font-weight-medium">
                {{ checkNullValue(item.employeeName) }}
              </section>
              <div
                v-if="!isMobileView"
                class="grey--text caption text-truncate"
                style="width: 120px; overflow-wrap: break-word"
              >
                {{
                  item.userDefinedEmployeeId ? item.userDefinedEmployeeId : ""
                }}
              </div>
            </div>
          </td>

          <!-- Candidate Name -->
          <td :class="isMobileView ? 'v-data-table__mobile-row' : ''">
            <div v-if="isMobileView" class="v-data-table__mobile-row__header">
              Candidate Name
            </div>
            <div :class="isMobileView ? 'v-data-table__mobile-row__cell' : ''">
              <section class="primary--text body-2 font-weight-medium">
                {{ checkNullValue(item.candidateName) }}
              </section>
              <div
                v-if="!isMobileView"
                class="grey--text caption text-truncate"
                style="width: 120px; overflow-wrap: break-word"
              >
                {{
                  item.userDefinedCandidateId ? item.userDefinedCandidateId : ""
                }}
              </div>
            </div>
          </td>

          <!-- Document Name -->
          <td :class="isMobileView ? 'v-data-table__mobile-row' : ''">
            <div v-if="isMobileView" class="v-data-table__mobile-row__header">
              Document Name
            </div>
            <div :class="isMobileView ? 'v-data-table__mobile-row__cell' : ''">
              <section class="primary--text body-2">
                {{ checkNullValue(item.documentName) }}
              </section>
            </div>
          </td>

          <!-- Signatories -->
          <td :class="isMobileView ? 'v-data-table__mobile-row' : ''">
            <div v-if="isMobileView" class="v-data-table__mobile-row__header">
              Signatories
            </div>
            <div
              :class="isMobileView ? 'v-data-table__mobile-row__cell' : ''"
              class="primary--text body-2"
            >
              <section
                v-if="
                  item.authorizedSignatories &&
                  item.authorizedSignatories.length > 0
                "
                class="d-flex align-center"
              >
                <v-menu :open-on-hover="!isMobileView" right offset-x>
                  <template #activator="{ on, attrs }">
                    <div v-bind="attrs" v-on="on">
                      <v-avatar
                        v-for="(signatory, index) in JSON.parse(
                          item.authorizedSignatories
                        )"
                        :key="index + 'avatar'"
                        size="31"
                        :color="
                          signatory.status === 'Signed'
                            ? '#00c619'
                            : 'grey lighten-1'
                        "
                        class="ml-1 caption white--text font-weight-medium"
                      >
                        {{ signatory.signatureEmployeeName | letterAvatar }}
                      </v-avatar>
                    </div>
                  </template>

                  <v-list>
                    <v-list-item
                      v-for="(signatory, index) in JSON.parse(
                        item.authorizedSignatories
                      )"
                      :key="index + 'menuList'"
                    >
                      <v-list-item-avatar
                        size="30"
                        class="avatarClass"
                        :color="
                          signatory.status === 'Signed'
                            ? '#00c619'
                            : 'grey lighten-1'
                        "
                        :class="['avatarBCColor' + (index % 5)]"
                      >
                        <span
                          class="mx-auto caption font-weight-medium white--text"
                          >{{
                            signatory.signatureEmployeeName | letterAvatar
                          }}</span
                        >
                      </v-list-item-avatar>
                      <v-list-item-content style="max-width: 160px">
                        <v-list-item-title
                          style="max-width: 150px"
                          class="text-truncate"
                        >
                          {{ signatory.signatureEmployeeName }}
                        </v-list-item-title>
                        <v-list-item-subtitle
                          :class="
                            signatory.status === 'Signed'
                              ? 'completed-status-text'
                              : 'grey--text'
                          "
                        >
                          {{ signatory.status }}
                        </v-list-item-subtitle>
                      </v-list-item-content>
                      <v-btn
                        v-if="
                          signatory.status === 'Not Signed' &&
                          signatory.emailId &&
                          !['Declined', 'Rejected', 'Draft'].includes(
                            item.status
                          )
                        "
                        color="primary"
                        small
                        class="mr-1"
                        style="max-width: 75px"
                        :disabled="
                          signatory?.signatureKey?.toLowerCase() ===
                            'candidate' &&
                          !checkIfSignedByAll(
                            JSON.parse(item?.authorizedSignatories)
                          )
                        "
                        @click.stop="resendEmailToSignatories(item, signatory)"
                      >
                        {{ signatory?.emailSent ? "Resend " : "Send" }}
                        <v-icon size="13" color="white"> send </v-icon>
                      </v-btn>
                      <v-btn
                        v-else-if="
                          signatory.status === 'Not Signed' &&
                          signatory.emailId &&
                          ['Declined', 'Rejected'].includes(item.status)
                        "
                        v-tooltip="{
                          content:
                            item.status === 'Declined'
                              ? 'Resend not allowed as the candidate declined the offer'
                              : item.status === 'Rejected'
                              ? 'Resend not allowed as the manager rejected the offer'
                              : '',
                          trigger: 'hover',
                          placement: 'bottom',
                        }"
                        color="grey"
                        small
                        class="mr-1"
                        style="
                          max-width: 75px;
                          cursor: not-allowed;
                          opacity: 0.5;
                        "
                      >
                        {{ signatory?.emailSent ? "Resend " : "Send" }}
                        <v-icon size="13" color="white"> send </v-icon>
                      </v-btn>
                    </v-list-item>
                  </v-list>
                </v-menu>
              </section>
              <section v-else>-</section>
            </div>
          </td>

          <!-- Status -->
          <td :class="isMobileView ? 'v-data-table__mobile-row' : ''">
            <div v-if="isMobileView" class="v-data-table__mobile-row__header">
              Status
            </div>
            <div :class="isMobileView ? 'v-data-table__mobile-row__cell' : ''">
              <section
                class="d-flex align-center"
                :class="
                  item.status === 'Completed'
                    ? 'completed-status-text'
                    : item.status === 'In Review'
                    ? 'in-review-status-text'
                    : item.status === 'Partially Signed'
                    ? 'pending-signed-status-text '
                    : item.status === 'Draft'
                    ? 'draft-status-text'
                    : item.status === 'Declined' || item.status === 'Rejected'
                    ? 'decline-status-text'
                    : ''
                "
              >
                <v-icon
                  v-if="
                    item.status === 'Completed' ||
                    item.status === 'Partially Signed'
                  "
                  :color="
                    item.status === 'Partially Signed'
                      ? '#357FCA'
                      : 'green accent-4'
                  "
                  size="25"
                  class="pr-1"
                >
                  fas fa-check-circle
                </v-icon>
                <v-avatar
                  v-else-if="item.status === 'In Review'"
                  color="#D6C814"
                  size="25"
                  class="mr-1"
                >
                  <v-icon color="white" size="13"> fas fa-search </v-icon>
                </v-avatar>
                <v-avatar
                  v-else-if="item.status === 'Draft'"
                  color="#FF7ACD"
                  size="25"
                  class="mr-1"
                >
                  <v-icon color="white" size="13"> create </v-icon>
                </v-avatar>
                <v-icon
                  v-else-if="
                    item.status === 'Declined' || item.status === 'Rejected'
                  "
                  color="red"
                  size="25"
                  class="pr-1"
                >
                  fas fa-times-circle
                </v-icon>
                <span class="body-2">{{ item.status }}</span>
              </section>
            </div>
          </td>
          <td :class="isMobileView ? 'v-data-table__mobile-row' : ''">
            <div v-if="isMobileView" class="v-data-table__mobile-row__header">
              Document Author
            </div>
            <div :class="isMobileView ? 'v-data-table__mobile-row__cell' : ''">
              <section class="primary--text body-2">
                {{ checkNullValue(item.addedBy) }}
              </section>
            </div>
          </td>
          <td :class="isMobileView ? 'v-data-table__mobile-row' : ''">
            <div v-if="isMobileView" class="v-data-table__mobile-row__header">
              Action
            </div>
            <div
              :class="
                isMobileView
                  ? 'v-data-table__mobile-row__cell'
                  : 'd-flex justify-end'
              "
            >
              <v-btn
                v-tooltip="{
                  content: 'Print',
                  trigger: 'hover',
                  placement: 'top',
                }"
                small
                color="white"
                dark
                class="mr-1"
                fab
                @click.stop="onClickPrint(item)"
              >
                <v-icon color="primary lighten-1"> fas fa-print </v-icon>
              </v-btn>
              <v-btn
                v-if="item.documentLink && item.status === 'Completed'"
                v-tooltip="{
                  content: 'PDF',
                  trigger: 'hover',
                  placement: 'top',
                }"
                small
                color="white"
                class="mr-1"
                fab
                @click.stop="downloadPDF(item)"
              >
                <v-icon color="primary lighten-1"> far fa-file-pdf </v-icon>
              </v-btn>
              <v-btn
                v-if="item.status === 'Completed'"
                v-tooltip="{
                  content: 'Send Email',
                  trigger: 'hover',
                  placement: 'top',
                }"
                color="white"
                dark
                small
                fab
                class="mr-1"
                @click.stop="sendEmailToEmployee(item)"
              >
                <v-icon color="primary lighten-1"> mail_outline </v-icon>
              </v-btn>
              <v-btn
                v-if="item.status !== 'Completed' && formAccess.delete"
                v-tooltip="{
                  content: 'Delete',
                  trigger: 'hover',
                  placement: 'bottom',
                }"
                color="white"
                dark
                small
                fab
                class="mr-1"
                @click.stop="
                  openDeleteConfirmationModal(item.generatedDocumentId)
                "
              >
                <v-icon color="primary lighten-1"> delete </v-icon>
              </v-btn>
            </div>
          </td>
        </tr>
      </template>
    </v-data-table>
    <AppWarningModel
      v-if="openDeleteConfirmation"
      :open-delete-confirmation="openDeleteConfirmation"
      image-name="common/delete-bin"
      close-button-text="Cancel"
      accept-button-text="Proceed"
      confirmation-content="Are you sure to delete the selected record?"
      @close-warning-modal="closeDeleteConfirmationModal()"
      @accept-modal="deleteEmpDocument()"
    />
    <AppLoading v-if="loadingScreen" />
    <DocGeneratorEmailNotification
      v-if="openSendEmailModal"
      :open-email-modal="openSendEmailModal"
      :notify-document-details="notifyDocumentDetails"
      @close-notify-modal="openSendEmailModal = false"
    />
  </div>
</template>

<script>
// import components
import ButtonDropdown from "@/components/helper-components/ButtonWithDropdown";
// queries
import { checkNullValue, getErrorCodes, handleNetworkErrors } from "@/helper";
import {
  DELETE_EMPLOYEE_GENERATED_DOCUMENT,
  RESEND_EMAIL_TO_SIGNATORIES,
} from "@/graphql/compliance-management/docuSignQueries";
const DocGeneratorEmailNotification = () => import("./EmailNotification");

export default {
  name: "ListDocumentGenerator",

  components: {
    ButtonDropdown,
    DocGeneratorEmailNotification,
  },

  props: {
    tableItems: {
      type: Array,
      required: true,
    },
    formAccess: {
      type: [Object, Boolean],
      required: true,
    },
  },

  data: () => ({
    // pagination
    pageCount: 1,
    page: 1,
    itemsPerPage: 50,
    pageNumber: 50,
    openPrintModal: false,
    tableHeaders: [
      {
        text: "Employee Name",
        value: "employeeName",
      },
      {
        text: "Candidate Name",
        value: "candidateName",
      },
      {
        text: "Document Name",
        value: "documentName",
      },
      {
        text: "Signatories",
        value: "authorizedSignatories",
      },
      {
        text: "Status",
        value: "status",
      },
      {
        text: "Document Author",
        value: "documentAuthor",
      },
      {
        text: "Action",
        value: "action",
        align: "right",
        sortable: false,
      },
    ],
    selectedRecords: [],
    selectedRecord: {},
    openDeleteConfirmation: false,
    errorContent: "",
    deleteEmpDocId: null,
    loadingScreen: false,
    openSendEmailModal: false,
    notifyDocumentDetails: "",
  }),

  computed: {
    // search in shares list
    searchValue() {
      return this.$store.state.empSearchValue;
    },

    isMobileView() {
      return this.$store.state.isMobileWindowSize;
    },

    windowWidth() {
      return this.$store.state.windowWidth;
    },
  },

  watch: {
    // when page number is changed
    pageNumber(val) {
      if (val === "All") {
        this.pageCount = 1;
        this.itemsPerPage = this.tableItems.length;
        this.page = 1;
      } else {
        let pageCount = this.tableItems.length / this.pageNumber;
        pageCount = pageCount <= 1 ? 1 : pageCount;
        this.pageCount = Math.round(pageCount);
        this.itemsPerPage = this.pageNumber;
        this.page = 1;
      }
    },
  },

  methods: {
    checkNullValue,
    // pageNumber is emitted from its child component to update the count in parent
    fnChangePaginationCount(val) {
      this.pageNumber = val;
    },

    // select the record to view
    selectDocTemplateRecord(selectedRecord) {
      this.selectedRecord = selectedRecord;
      this.$emit("action-on-doc-generator", [selectedRecord, "view"]);
    },

    downloadPDF(selectedRecord) {
      this.$emit("action-on-doc-generator", [selectedRecord, "download"]);
    },

    // on delete employees generated document
    deleteEmpDocument() {
      let vm = this;
      vm.loadingScreen = true;
      vm.closeDeleteConfirmationModal();
      try {
        vm.$apollo
          .mutate({
            mutation: DELETE_EMPLOYEE_GENERATED_DOCUMENT,
            variables: {
              generatedDocumentId: vm.deleteEmpDocId,
            },
            client: "apolloClientP",
          })
          .then(() => {
            vm.loadingScreen = false;
            let snackbarData = {
              isOpen: true,
              type: "success",
              message: "Document deleted successfully.",
            };
            vm.clearFilter(); // clear search value
            vm.showAlert(snackbarData);
            vm.deleteEmpDocId = null;
            vm.$emit("delete-success");
          })
          .catch((deleteError) => {
            vm.handleDeleteError(deleteError);
          });
      } catch {
        vm.handleDeleteError();
      }
    },

    // resend email to signatories
    resendEmailToSignatories(selectedItem, selectedSignatory) {
      let vm = this;
      vm.loadingScreen = true;
      try {
        const { generatedDocumentId, documentName } = selectedItem;
        const {
          signatureEmployeeId,
          signatureEmployeeName,
          emailId,
          signatureKey,
        } = selectedSignatory;
        let isCandidate = signatureKey === "Candidate" ? 1 : 0;
        vm.$apollo
          .mutate({
            mutation: RESEND_EMAIL_TO_SIGNATORIES,
            variables: {
              documentId: generatedDocumentId,
              documentName: documentName,
              signatoryId: signatureEmployeeId,
              signatoryEmailAddress: emailId,
              signatoryName: signatureEmployeeName,
              isCandidate: isCandidate,
            },
            client: "apolloClientO",
          })
          .then(() => {
            vm.loadingScreen = false;
            let snackbarData = {
              isOpen: true,
              type: "success",
              message: "Email resent successfully to the signatory",
            };
            vm.showAlert(snackbarData);
          })
          .catch((resendErr) => {
            vm.handleResendEmailError(resendErr);
          });
      } catch {
        vm.handleResendEmailError();
      }
    },

    // open warning alert before triggering the delete event
    openDeleteConfirmationModal(deleteId) {
      this.deleteEmpDocId = deleteId;
      this.openDeleteConfirmation = true;
    },

    // while closing the delete warning alert
    closeDeleteConfirmationModal() {
      this.openDeleteConfirmation = false;
    },

    // send notification
    sendEmailToEmployee(item) {
      const { employeeEmail } = this.$store.state.userDetails;
      if (employeeEmail) {
        this.notifyDocumentDetails = item;
        this.openSendEmailModal = true;
      } else {
        let snackbarData = {
          isOpen: true,
          type: "warning",
          message:
            "Your account is not associated with an email address. Hence you cannot share a document with others.",
        };
        this.showAlert(snackbarData);
      }
    },

    // clear search and filter data
    clearFilter() {
      this.$store.commit("UPDATE_TOPBAR_CLEAR_FLAG", true);
      this.$store.commit("UPDATE_EMP_SEARCH_VAL", "");
    },

    // while clicking print in the grid, we have to open the content in popup
    onClickPrint(selectedItem) {
      this.$emit("action-on-doc-generator", [selectedItem, "print"]);
    },

    // handle doc template delete error from backend
    handleDeleteError(err = "") {
      let snackbarData = {
        isOpen: true,
        type: "warning",
        message: "",
      };
      this.loadingScreen = false;
      this.deleteEmpDocId = null;
      // check if it is a graphql error
      if (err && err.graphQLErrors) {
        let errorCode = getErrorCodes(err);
        switch (errorCode) {
          case "_DB0000": // technical issues.
            snackbarData["message"] =
              "It’s us! There seems to be some technical difficulties. Please try after some time.";
            break;
          case "_DB0103": // no delete access
            snackbarData["message"] =
              "Sorry, you don't have access to delete the employee's generated document. Please contact HR administrator.";
            break;
          case "_EC0006": // Record(s) are already deleted in the same or some other user session.
            snackbarData["message"] =
              "Unable to delete the record as it was deleted already in the same or some other user session.";
            break;
          case "_UH0001": //unhandled error
          case "_DB0001": // Error while retrieving the employee access rights
          case "_DB0104": // While check access rights form not found
          case "_DB0002": // Error while checking the employee access rights
          case "CDG0105": // Error while processing the request to delete the generated document.
          case "CDG0006": // Error while deleting the generated document.
          case "_EC0007": // Invalid input field(s).
          default:
            snackbarData["message"] =
              "Something went wrong while deleting the employee's generated document. If you continue to see this issue, please contact the platform administrator.";
            break;
        }
      } else if (err && err.networkError) {
        snackbarData["message"] = handleNetworkErrors(err);
      } else {
        snackbarData["message"] =
          "Something went wrong while deleting the employee's generated document. Please try after some time.";
      }
      this.showAlert(snackbarData);
    },

    // handle resend email error
    handleResendEmailError(err = "") {
      let snackbarData = {
        isOpen: true,
        type: "warning",
        message: "",
      };
      this.loadingScreen = false;
      // check if it is a graphql error
      if (err && err.graphQLErrors) {
        let errorCode = getErrorCodes(err);
        switch (errorCode) {
          case "_DB0000": // technical issues.
            snackbarData["message"] =
              "It’s us! There seems to be some technical difficulties. Please try after some time.";
            break;
          case "_EC0007": // Invalid input field(s).
          case "_UH0001": // unhandled error
          case "PBP0105": // Error in sending email
          case "CDG0123": // Error while processing the request to resend the signature link to the signatory.
          case "CDG0016": // Error while resending the signature link to the signatory.
          default:
            snackbarData["message"] =
              "Something went wrong while resending the document to signatory. If you continue to see this issue, please contact the platform administrator.";
            break;
        }
      } else if (err && err.networkError) {
        snackbarData["message"] = handleNetworkErrors(err);
      } else {
        snackbarData["message"] =
          "Something went wrong while resending the document to signatory. Please try after some time.";
      }
      this.showAlert(snackbarData);
    },
    checkIfSignedByAll(data) {
      const allNonCandidatesSigned = data
        .filter((item) => item.signatureKey.toLowerCase() !== "candidate")
        .every((item) => item.status.toLowerCase() === "signed");
      return allNonCandidatesSigned;
    },

    // show the message in snack bar
    showAlert(snackbarData) {
      this.$store.commit("OPEN_SNACKBAR", snackbarData);
    },
  },
};
</script>

<style scoped>
.docgen-custom-table-headers {
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  display: inline;
}
.completed-status-text {
  color: #00c619 !important;
}
.in-review-status-text {
  color: #d6c814 !important;
}
.pending-signed-status-text {
  color: #357fca !important;
}
.draft-status-text {
  color: #ff7acd !important;
}
.decline-status-text {
  color: red !important;
}
</style>
